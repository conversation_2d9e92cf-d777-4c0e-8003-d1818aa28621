const express = require('express');
const path = require('path');
const app = express();
const PORT = 3000;

// Serve static files (CSS, JS, images)
app.use(express.static('.'));

// Route for root path
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

// Routes for clean URLs without .html extension
const routes = ['subjects', 'team', 'about', 'faq'];

routes.forEach(route => {
    app.get(`/${route}`, (req, res) => {
        res.sendFile(path.join(__dirname, `${route}.html`));
    });
});

// Handle 404 errors
app.use((req, res) => {
    res.status(404).sendFile(path.join(__dirname, 'index.html'));
});

app.listen(PORT, () => {
    console.log(`🚀 Server running at http://localhost:${PORT}`);
    console.log('📋 Available routes:');
    console.log('   • http://localhost:3000/');
    console.log('   • http://localhost:3000/subjects');
    console.log('   • http://localhost:3000/team');
    console.log('   • http://localhost:3000/about');
    console.log('   • http://localhost:3000/faq');
}); 