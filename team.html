<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Team - xjpcnm.com</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
    <style>
        .team-section {
            padding: 8rem 5% 4rem;
        }

        .team-header {
            text-align: center;
            margin-bottom: 4rem;
        }

        .team-header h1 {
            color: var(--text-color);
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }

        .team-header p {
            color: #666;
            max-width: 600px;
            margin: 0 auto;
        }

        .team-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
        }

        .team-member {
            background-color: var(--card-background);
            border-radius: 10px;
            padding: 2rem;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .member-image {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            margin: 0 auto 1.5rem;
            overflow: hidden;
            border: 3px solid var(--primary-color);
        }

        .member-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .member-info h3 {
            color: var(--text-color);
            margin-bottom: 0.5rem;
        }

        .member-role {
            color: var(--primary-color);
            font-weight: 500;
            margin-bottom: 1rem;
        }

        .member-bio {
            color: #666;
            margin-bottom: 1.5rem;
        }

        .social-links {
            display: flex;
            justify-content: center;
            gap: 1rem;
        }

        .social-links a {
            color: var(--text-color);
            font-size: 1.2rem;
            transition: color 0.3s ease;
        }

        .social-links a:hover {
            color: var(--primary-color);
        }
    </style>
</head>
<body>
    <header>
        <nav class="navbar">
            <div class="logo">
                <h1>xjpcnm.com</h1>
            </div>
            <ul class="nav-links">
                <li><a href="/">Home</a></li>
                <li><a href="/subjects">Subjects</a></li>
                <li><a href="/team" class="active">Team</a></li>
                <li><a href="/about">About Us</a></li>
                <li><a href="/faq">FAQ</a></li>
            </ul>
            <button class="waitlist-btn">Join Waitlist</button>
        </nav>
    </header>

    <main class="team-section">
        <div class="team-header">
            <h1>Meet Our Team</h1>
            <p>A group of passionate educators and technologists working together to revolutionize personalized learning through AI.</p>
        </div>

        <div class="team-grid">
            <div class="team-member">
                <div class="member-image">
                    <img src="assets/team1.png" alt="Jonald Bob">
                </div>
                <div class="member-info">
                    <h3>Jonald Bob</h3>
                    <div class="member-role">Co-founder & CEO</div>
                    <p class="member-bio">Focused on AI education technology, leading the team in innovative learning methods.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-linkedin"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                    </div>
                </div>
            </div>

            <div class="team-member">
                <div class="member-image">
                    <img src="assets/team2.png" alt="Sarah Johnson">
                </div>
                <div class="member-info">
                    <h3>Sarah Johnson</h3>
                    <div class="member-role">Head of Education</div>
                    <p class="member-bio">Expert in curriculum development and personalized learning strategies.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-linkedin"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                    </div>
                </div>
            </div>

            <div class="team-member">
                <div class="member-image">
                    <img src="assets/team3.png" alt="Michael Chen">
                </div>
                <div class="member-info">
                    <h3>Michael Chen</h3>
                    <div class="member-role">Chief Technology Officer</div>
                    <p class="member-bio">Leading AI development and platform architecture for optimal learning experiences.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-linkedin"></i></a>
                        <a href="#"><i class="fab fa-github"></i></a>
                    </div>
                </div>
            </div>

            <div class="team-member">
                <div class="member-image">
                    <img src="assets/team4.png" alt="Emily Wang">
                </div>
                <div class="member-info">
                    <h3>Emily Wang</h3>
                    <div class="member-role">UX Design Lead</div>
                    <p class="member-bio">Creating intuitive and engaging learning interfaces for students of all ages.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-linkedin"></i></a>
                        <a href="#"><i class="fab fa-dribbble"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <footer>
        <p>© 2025 xjpcnm Educational Services Pvt. Ltd. All rights reserved.</p>
    </footer>

    <script>
        // Make page visible
        document.addEventListener('DOMContentLoaded', function() {
            document.body.style.opacity = '1';
            document.body.style.transition = 'opacity 0.5s ease';
        });
        
        // Add hover effects for team members
        document.querySelectorAll('.team-member').forEach(member => {
            member.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-10px)';
                this.style.transition = 'transform 0.3s ease';
            });
            
            member.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });
        
        // Waitlist button functionality
        const waitlistBtn = document.querySelector('.waitlist-btn');
        if (waitlistBtn) {
            waitlistBtn.addEventListener('click', function() {
                alert('Thank you for your interest! We will contact you soon.');
            });
        }
    </script>
</body>
</html> 