<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FAQ - xjpcnm.com</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
    <style>
        .faq-section {
            padding: 8rem 5% 4rem;
        }

        .faq-header {
            text-align: center;
            margin-bottom: 4rem;
        }

        .faq-header h1 {
            color: var(--text-color);
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }

        .faq-header p {
            color: #666;
            max-width: 600px;
            margin: 0 auto;
        }

        .faq-container {
            max-width: 800px;
            margin: 0 auto;
        }

        .faq-item {
            background-color: var(--card-background);
            border-radius: 10px;
            margin-bottom: 1.5rem;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .faq-question {
            padding: 1.5rem;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: var(--text-color);
            font-weight: 500;
        }

        .faq-question i {
            transition: transform 0.3s ease;
        }

        .faq-answer {
            padding: 0 1.5rem;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease, padding 0.3s ease;
            color: #666;
            line-height: 1.6;
        }

        .faq-item.active .faq-question i {
            transform: rotate(180deg);
        }

        .faq-item.active .faq-answer {
            padding: 0 1.5rem 1.5rem;
            max-height: 500px;
        }

        .contact-support {
            text-align: center;
            margin-top: 4rem;
            padding: 3rem;
            background-color: var(--card-background);
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .contact-support h2 {
            color: var(--text-color);
            margin-bottom: 1rem;
        }

        .contact-support p {
            color: #666;
            margin-bottom: 2rem;
        }

        .support-btn {
            background-color: var(--primary-color);
            color: white;
            padding: 0.8rem 2rem;
            border-radius: 25px;
            text-decoration: none;
            display: inline-block;
            transition: background-color 0.3s ease;
        }

        .support-btn:hover {
            background-color: #45a049;
        }
    </style>
</head>
<body>
    <header>
        <nav class="navbar">
            <div class="logo">
                <h1>xjpcnm.com</h1>
            </div>
            <ul class="nav-links">
                <li><a href="/">Home</a></li>
                <li><a href="/subjects">Subjects</a></li>
                <li><a href="/team">Team</a></li>
                <li><a href="/about">About Us</a></li>
                <li><a href="/faq" class="active">FAQ</a></li>
            </ul>
            <button class="waitlist-btn">Join Waitlist</button>
        </nav>
    </header>

    <main class="faq-section">
        <div class="faq-header">
            <h1>Frequently Asked Questions</h1>
            <p>Find answers to common questions about our AI-powered learning platform.</p>
        </div>

        <div class="faq-container">
            <div class="faq-item">
                <div class="faq-question">
                    <span>How does the AI personalization work?</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="faq-answer">
                    <p>Our AI system analyzes your learning patterns, preferences, and progress to create a personalized learning path. It adapts in real-time to your performance and adjusts the difficulty and style of content delivery accordingly.</p>
                </div>
            </div>

            <div class="faq-item">
                <div class="faq-question">
                    <span>What subjects are currently available?</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="faq-answer">
                    <p>We currently offer Mathematics, Science, English, Social Studies, Computer Science, and Arts. Each subject is carefully curated with AI-enhanced content and interactive learning materials.</p>
                </div>
            </div>

            <div class="faq-item">
                <div class="faq-question">
                    <span>How much does it cost?</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="faq-answer">
                    <p>We offer flexible subscription plans starting from $29.99/month. Each plan includes full access to our AI-powered learning platform, progress tracking, and personalized support.</p>
                </div>
            </div>

            <div class="faq-item">
                <div class="faq-question">
                    <span>Is there a free trial available?</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="faq-answer">
                    <p>Yes! We offer a 14-day free trial for all new users. During this period, you'll have full access to all features and subjects to experience our AI-powered learning platform.</p>
                </div>
            </div>

            <div class="faq-item">
                <div class="faq-question">
                    <span>How do I track my child's progress?</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="faq-answer">
                    <p>Parents have access to a dedicated dashboard showing detailed progress reports, learning analytics, and areas of improvement. You can monitor your child's engagement, achievements, and learning journey in real-time.</p>
                </div>
            </div>
        </div>

        <div class="contact-support">
            <h2>Still Have Questions?</h2>
            <p>Our support team is here to help you with any questions or concerns.</p>
            <a href="#" class="support-btn">Contact Support</a>
        </div>
    </main>

    <footer>
        <p>© 2025 xjpcnm Educational Services Pvt. Ltd. All rights reserved.</p>
    </footer>

    <script>
        // Make page visible
        document.addEventListener('DOMContentLoaded', function() {
            document.body.style.opacity = '1';
            document.body.style.transition = 'opacity 0.5s ease';
        });
        
        // FAQ functionality
        document.querySelectorAll('.faq-question').forEach(question => {
            question.addEventListener('click', () => {
                const item = question.parentElement;
                const isActive = item.classList.contains('active');
                
                // Close all other FAQ items
                document.querySelectorAll('.faq-item').forEach(faqItem => {
                    faqItem.classList.remove('active');
                });
                
                // Toggle current item
                if (!isActive) {
                    item.classList.add('active');
                }
            });
        });
        
        // Waitlist button functionality
        const waitlistBtn = document.querySelector('.waitlist-btn');
        if (waitlistBtn) {
            waitlistBtn.addEventListener('click', function() {
                alert('Thank you for your interest! We will contact you soon.');
            });
        }
        
        // Support button functionality
        const supportBtn = document.querySelector('.support-btn');
        if (supportBtn) {
            supportBtn.addEventListener('click', function(e) {
                e.preventDefault();
                alert('Our support team will contact you within 24 hours. Email: <EMAIL>');
            });
        }
    </script>
</body>
</html> 