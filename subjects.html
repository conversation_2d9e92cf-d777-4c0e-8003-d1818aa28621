<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Subjects - xjpcnm.com</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
    <style>
        .subjects-grid {
            padding: 8rem 5% 4rem;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
        }

        .subject-card {
            background-color: var(--card-background);
            border-radius: 10px;
            padding: 2rem;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .subject-card:hover {
            transform: translateY(-5px);
        }

        .subject-card i {
            font-size: 3rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }

        .subject-card h3 {
            color: var(--text-color);
            margin-bottom: 1rem;
        }

        .subject-card p {
            color: #666;
            margin-bottom: 1.5rem;
        }

        .view-courses-btn {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 0.8rem 1.5rem;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 500;
            transition: background-color 0.3s ease;
        }

        .view-courses-btn:hover {
            background-color: #45a049;
        }

        .filters {
            padding: 7rem 5% 0;
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            align-items: center;
        }

        .filter-select {
            padding: 0.5rem 1rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: white;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <header>
        <nav class="navbar">
            <div class="logo">
                <h1>xjpcnm.com</h1>
            </div>
            <ul class="nav-links">
                <li><a href="/">Home</a></li>
                <li><a href="/subjects" class="active">Subjects</a></li>
                <li><a href="/team">Team</a></li>
                <li><a href="/about">About Us</a></li>
                <li><a href="/faq">FAQ</a></li>
            </ul>
            <button class="waitlist-btn">Join Waitlist</button>
        </nav>
    </header>

    <section class="filters">
        <select class="filter-select">
            <option value="">Grade Level</option>
            <option value="elementary">Elementary</option>
            <option value="middle">Middle School</option>
            <option value="high">High School</option>
        </select>
        <select class="filter-select">
            <option value="">Difficulty</option>
            <option value="beginner">Beginner</option>
            <option value="intermediate">Intermediate</option>
            <option value="advanced">Advanced</option>
        </select>
    </section>

    <main class="subjects-grid">
        <div class="subject-card">
            <i class="fas fa-square-root-alt"></i>
            <h3>Mathematics</h3>
            <p>Optimize your math learning path with AI</p>
            <button class="view-courses-btn">View Courses</button>
        </div>

        <div class="subject-card">
            <i class="fas fa-atom"></i>
            <h3>Science</h3>
            <p>Explore scientific concepts with interactive learning</p>
            <button class="view-courses-btn">View Courses</button>
        </div>

        <div class="subject-card">
            <i class="fas fa-language"></i>
            <h3>English</h3>
            <p>Master language skills through AI-guided practice</p>
            <button class="view-courses-btn">View Courses</button>
        </div>

        <div class="subject-card">
            <i class="fas fa-globe-americas"></i>
            <h3>Social Studies</h3>
            <p>Discover history and culture with personalized lessons</p>
            <button class="view-courses-btn">View Courses</button>
        </div>

        <div class="subject-card">
            <i class="fas fa-laptop-code"></i>
            <h3>Computer Science</h3>
            <p>Learn programming with adaptive guidance</p>
            <button class="view-courses-btn">View Courses</button>
        </div>

        <div class="subject-card">
            <i class="fas fa-palette"></i>
            <h3>Arts</h3>
            <p>Develop creativity with AI-enhanced art education</p>
            <button class="view-courses-btn">View Courses</button>
        </div>
    </main>

    <footer>
        <p>© 2025 xjpcnm Educational Services Pvt. Ltd. All rights reserved.</p>
    </footer>

    <script>
        // Make page visible
        document.addEventListener('DOMContentLoaded', function() {
            document.body.style.opacity = '1';
            document.body.style.transition = 'opacity 0.5s ease';
        });
        
        // Add basic interactivity
        document.querySelectorAll('.view-courses-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                alert('Course details coming soon! Join our waitlist to be notified.');
            });
        });
        
        // Waitlist button functionality
        const waitlistBtn = document.querySelector('.waitlist-btn');
        if (waitlistBtn) {
            waitlistBtn.addEventListener('click', function() {
                alert('Thank you for your interest! We will contact you soon.');
            });
        }
    </script>
</body>
</html> 