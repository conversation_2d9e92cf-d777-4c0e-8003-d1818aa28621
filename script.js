// Enhanced JavaScript for xjpcnm.com - Enhanced Interactive Script

document.addEventListener('DOMContentLoaded', function() {
    
    // Navbar scroll effect
    const navbar = document.querySelector('.navbar');
    let lastScrollTop = 0;
    
    window.addEventListener('scroll', function() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        
        // Add scroll style
        if (scrollTop > 100) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
        
        // Navbar hide/show effect
        if (scrollTop > lastScrollTop && scrollTop > 200) {
            navbar.style.transform = 'translateY(-100%)';
        } else {
            navbar.style.transform = 'translateY(0)';
        }
        lastScrollTop = scrollTop;
    });
    
    // Smooth scrolling effect
    const links = document.querySelectorAll('a[href^="#"]');
    for (const link of links) {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const href = this.getAttribute('href');
            const target = document.querySelector(href);
            if (target) {
                const offsetTop = target.offsetTop - 80;
                window.scrollTo({
                    top: offsetTop,
                    behavior: "smooth"
                });
            }
        });
    }
    
    // Scroll animation observer
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animated');
                
                // If it's statistics section, start counting animation
                if (entry.target.classList.contains('stats-section')) {
                    animateStats();
                }
            }
        });
    }, observerOptions);
    
    // Observe all elements that need animation
    const animateElements = document.querySelectorAll('.feature-card, .highlight-card, .testimonial-card, .news-card, .stats-section');
    animateElements.forEach(el => {
        el.classList.add('animate-on-scroll');
        observer.observe(el);
    });
    
    // Statistics counting animation
    function animateStats() {
        const statNumbers = document.querySelectorAll('.stat-number');
        statNumbers.forEach(stat => {
            const target = parseInt(stat.textContent.replace(/[^\d]/g, ''));
            const suffix = stat.textContent.replace(/[\d,]/g, '');
            let current = 0;
            const increment = target / 100;
            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }
                
                if (suffix.includes('%')) {
                    stat.textContent = Math.floor(current) + '%';
                } else if (suffix.includes('+')) {
                    stat.textContent = Math.floor(current).toLocaleString() + '+';
                } else if (suffix.includes('/')) {
                    stat.textContent = Math.floor(current) + '/7';
                } else {
                    stat.textContent = Math.floor(current).toLocaleString() + suffix;
                }
            }, 20);
        });
    }
    
    // Golden circle interactive effects
    const circles = document.querySelectorAll('.circle');
    circles.forEach((circle, index) => {
        circle.addEventListener('mouseenter', function() {
            this.style.transform = `translate(-50%, -50%) scale(1.15)`;
            this.style.boxShadow = '0 10px 30px rgba(0,0,0,0.3)';
        });
        
        circle.addEventListener('mouseleave', function() {
            this.style.transform = `translate(-50%, -50%) scale(1)`;
            this.style.boxShadow = 'none';
        });
        
        // Click effect
        circle.addEventListener('click', function() {
            const messages = {
                'why': 'Why learn? Explore the essential motivation and meaning of learning.',
                'how': 'How to learn? Master scientific and efficient learning methods.',
                'what': 'What to learn? Choose learning content that suits you.'
            };
            
            const circleType = this.textContent.trim();
            let message = '';
            
            if (circleType === 'Why') message = messages.why;
            else if (circleType === 'How') message = messages.how;
            else if (circleType === 'What') message = messages.what;
            
            showTooltip(this, message);
        });
    });
    
    // Tooltip functionality
    function showTooltip(element, message) {
        // Remove existing tooltip
        const existingTooltip = document.querySelector('.custom-tooltip');
        if (existingTooltip) {
            existingTooltip.remove();
        }
        
        const tooltip = document.createElement('div');
        tooltip.className = 'custom-tooltip';
        tooltip.textContent = message;
        tooltip.style.cssText = `
            position: absolute;
            background: rgba(0,0,0,0.9);
            color: white;
            padding: 10px 15px;
            border-radius: 8px;
            font-size: 14px;
            max-width: 200px;
            z-index: 1000;
            opacity: 0;
            transform: translateY(10px);
            transition: all 0.3s ease;
            pointer-events: none;
        `;
        
        document.body.appendChild(tooltip);
        
        const rect = element.getBoundingClientRect();
        tooltip.style.left = (rect.left + rect.width / 2 - tooltip.offsetWidth / 2) + 'px';
        tooltip.style.top = (rect.bottom + 10) + 'px';
        
        // Show animation
        setTimeout(() => {
            tooltip.style.opacity = '1';
            tooltip.style.transform = 'translateY(0)';
        }, 10);
        
        // Auto disappear after 3 seconds
        setTimeout(() => {
            tooltip.style.opacity = '0';
            tooltip.style.transform = 'translateY(-10px)';
            setTimeout(() => tooltip.remove(), 300);
        }, 3000);
    }
    
    // Waitlist button functionality
    const waitlistBtn = document.querySelector('.waitlist-btn');
    if (waitlistBtn) {
        waitlistBtn.addEventListener('click', function(e) {
            e.preventDefault();
            showModal();
        });
    }
    
    // CTA button functionality
    const ctaBtns = document.querySelectorAll('.cta-btn');
    ctaBtns.forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            if (this.classList.contains('primary')) {
                showModal();
            } else {
                // Scroll to highlights section
                const highlightsSection = document.querySelector('.highlights-section');
                if (highlightsSection) {
                    highlightsSection.scrollIntoView({ behavior: 'smooth' });
                }
            }
        });
    });
    
    // Modal functionality
    function showModal() {
        // Create modal
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Join Our Learning Community</h3>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <p>Thank you for your interest in our AI personalized learning platform! Please leave your contact information and we will get in touch with you soon.</p>
                    <form class="waitlist-form">
                        <input type="text" placeholder="Your Name" required>
                        <input type="email" placeholder="Email Address" required>
                        <input type="tel" placeholder="Phone Number" required>
                        <select required>
                            <option value="">Select Your Subject of Interest</option>
                            <option value="math">Mathematics</option>
                            <option value="science">Science</option>
                            <option value="literature">Literature</option>
                            <option value="programming">Programming</option>
                            <option value="design">Design</option>
                            <option value="business">Business</option>
                        </select>
                        <button type="submit" class="submit-btn">Submit Application</button>
                    </form>
                </div>
            </div>
        `;
        
        // Add styles
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            opacity: 0;
            transition: opacity 0.3s ease;
        `;
        
        const modalContent = modal.querySelector('.modal-content');
        modalContent.style.cssText = `
            background: white;
            border-radius: 15px;
            padding: 2rem;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            transform: scale(0.8);
            transition: transform 0.3s ease;
        `;
        
        const modalHeader = modal.querySelector('.modal-header');
        modalHeader.style.cssText = `
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #eee;
        `;
        
        const closeBtn = modal.querySelector('.modal-close');
        closeBtn.style.cssText = `
            background: none;
            border: none;
            font-size: 2rem;
            cursor: pointer;
            color: #666;
            transition: color 0.3s ease;
        `;
        
        const form = modal.querySelector('.waitlist-form');
        form.style.cssText = `
            display: flex;
            flex-direction: column;
            gap: 1rem;
        `;
        
        const inputs = modal.querySelectorAll('input, select');
        inputs.forEach(input => {
            input.style.cssText = `
                padding: 12px;
                border: 2px solid #ddd;
                border-radius: 8px;
                font-size: 16px;
                transition: border-color 0.3s ease;
            `;
        });
        
        const submitBtn = modal.querySelector('.submit-btn');
        submitBtn.style.cssText = `
            background: linear-gradient(135deg, #4CAF50, #2196F3);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.3s ease;
        `;
        
        document.body.appendChild(modal);
        
        // Show animation
        setTimeout(() => {
            modal.style.opacity = '1';
            modalContent.style.transform = 'scale(1)';
        }, 10);
        
        // Close functionality
        function closeModal() {
            modal.style.opacity = '0';
            modalContent.style.transform = 'scale(0.8)';
            setTimeout(() => modal.remove(), 300);
        }
        
        closeBtn.addEventListener('click', closeModal);
        modal.addEventListener('click', function(e) {
            if (e.target === modal) closeModal();
        });
        
        // Form submission
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            submitBtn.textContent = 'Submitting...';
            submitBtn.disabled = true;
            
            // Simulate submission process
            setTimeout(() => {
                alert('Thank you for your application! We will contact you within 24 hours.');
                closeModal();
            }, 1500);
        });
        
        // Input focus effects
        inputs.forEach(input => {
            input.addEventListener('focus', function() {
                this.style.borderColor = '#4CAF50';
            });
            input.addEventListener('blur', function() {
                this.style.borderColor = '#ddd';
            });
        });
        
        // Submit button hover effect
        submitBtn.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });
        submitBtn.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    }
    
    // Enhanced card hover effects
    const cards = document.querySelectorAll('.feature-card, .highlight-card, .testimonial-card, .news-card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px) scale(1.02)';
        });
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
    
    // Initialization animation after page load
    window.addEventListener('load', function() {
        document.body.style.opacity = '1';
        
        // Add fade-in effect to page
        document.body.style.transition = 'opacity 0.5s ease';
        
        // Initialize all animation elements
        const allAnimateElements = document.querySelectorAll('.animate-on-scroll');
        allAnimateElements.forEach((el, index) => {
            el.style.animationDelay = (index * 0.1) + 's';
        });
    });
    
    // Keyboard navigation support
    document.addEventListener('keydown', function(e) {
        // ESC key to close modal
        if (e.key === 'Escape') {
            const modal = document.querySelector('.modal-overlay');
            if (modal) {
                modal.click();
            }
        }
    });
    
    // Performance optimization: throttle scroll events
    let ticking = false;
    function updateOnScroll() {
        // More scroll-related updates can be added here
        ticking = false;
    }
    
    window.addEventListener('scroll', function() {
        if (!ticking) {
            requestAnimationFrame(updateOnScroll);
            ticking = true;
        }
    });
    
    console.log('🎉 xjpcnm.com loaded successfully! Welcome to the AI personalized learning platform!');
}); 