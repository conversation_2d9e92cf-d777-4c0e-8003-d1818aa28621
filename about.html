<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>About Us - xjpcnm.com</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
    <style>
        .about-section {
            padding: 8rem 5% 4rem;
        }

        .about-header {
            text-align: center;
            margin-bottom: 4rem;
        }

        .about-header h1 {
            color: var(--text-color);
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }

        .about-content {
            max-width: 800px;
            margin: 0 auto;
        }

        .mission-section {
            background-color: var(--card-background);
            border-radius: 10px;
            padding: 3rem;
            margin-bottom: 3rem;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .mission-section h2 {
            color: var(--primary-color);
            margin-bottom: 1.5rem;
            font-size: 1.8rem;
        }

        .mission-section p {
            color: #666;
            line-height: 1.8;
            margin-bottom: 1.5rem;
        }

        .values-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .value-card {
            background-color: var(--card-background);
            border-radius: 10px;
            padding: 2rem;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .value-card i {
            font-size: 2.5rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }

        .value-card h3 {
            color: var(--text-color);
            margin-bottom: 1rem;
        }

        .value-card p {
            color: #666;
        }

        .contact-section {
            text-align: center;
            margin-top: 4rem;
            padding: 3rem;
            background-color: var(--card-background);
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .contact-section h2 {
            color: var(--text-color);
            margin-bottom: 1.5rem;
        }

        .contact-info {
            display: flex;
            justify-content: center;
            gap: 3rem;
            margin-top: 2rem;
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .contact-item i {
            color: var(--primary-color);
            font-size: 1.2rem;
        }
    </style>
</head>
<body>
    <header>
        <nav class="navbar">
            <div class="logo">
                <h1>xjpcnm.com</h1>
            </div>
            <ul class="nav-links">
                <li><a href="/">Home</a></li>
                <li><a href="/subjects">Subjects</a></li>
                <li><a href="/team">Team</a></li>
                <li><a href="/about" class="active">About Us</a></li>
                <li><a href="/faq">FAQ</a></li>
            </ul>
            <button class="waitlist-btn">Join Waitlist</button>
        </nav>
    </header>

    <main class="about-section">
        <div class="about-header">
            <h1>About Us - Our Mission</h1>
        </div>

        <div class="about-content">
            <div class="mission-section">
                <h2>Our Mission</h2>
                <p>At xjpcnm.com, we're revolutionizing education through AI-driven personalized learning. Our mission is to empower students to understand not just the 'what' and 'how' of learning, but most importantly, the 'why'.</p>
                <p>We believe that when students grasp the fundamental reasons behind concepts, they develop deeper understanding and lasting knowledge. Our AI-powered platform adapts to each student's unique learning style, pace, and interests.</p>
            </div>

            <div class="values-grid">
                <div class="value-card">
                    <i class="fas fa-brain"></i>
                    <h3>Innovation</h3>
                    <p>Continuously pushing the boundaries of AI in education to create better learning experiences.</p>
                </div>

                <div class="value-card">
                    <i class="fas fa-users"></i>
                    <h3>Personalization</h3>
                    <p>Tailoring education to each student's individual needs and learning style.</p>
                </div>

                <div class="value-card">
                    <i class="fas fa-chart-line"></i>
                    <h3>Growth</h3>
                    <p>Fostering continuous improvement and lifelong learning in our students.</p>
                </div>
            </div>

            <div class="contact-section">
                <h2>Join Us in Shaping the Future of Education</h2>
                <p>We're always looking for passionate educators, technologists, and partners to join our mission.</p>
                <div class="contact-info">
                    <div class="contact-item">
                        <i class="fas fa-envelope"></i>
                        <span><EMAIL></span>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-phone"></i>
                        <span>+****************</span>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <footer>
        <p>© 2025 xjpcnm Educational Services Pvt. Ltd. All rights reserved.</p>
    </footer>

    <script>
        // Make page visible
        document.addEventListener('DOMContentLoaded', function() {
            document.body.style.opacity = '1';
            document.body.style.transition = 'opacity 0.5s ease';
        });
        
        // Add hover effects for value cards
        document.querySelectorAll('.value-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px)';
                this.style.transition = 'transform 0.3s ease';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });
        
        // Waitlist button functionality
        const waitlistBtn = document.querySelector('.waitlist-btn');
        if (waitlistBtn) {
            waitlistBtn.addEventListener('click', function() {
                alert('Thank you for your interest! We will contact you soon.');
            });
        }
    </script>
</body>
</html> 