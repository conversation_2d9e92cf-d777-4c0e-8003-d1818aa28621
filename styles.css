:root {
    --primary-color: #4CAF50;
    --secondary-color: #2196F3;
    --text-color: #333;
    --background-color: #f9f9f9;
    --card-background: #ffffff;
    --accent-color: #FF6B6B;
    --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    --gradient-accent: linear-gradient(135deg, var(--accent-color), #FF8E53);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Poppins', sans-serif;
}

body {
    background-color: var(--background-color);
    color: var(--text-color);
    line-height: 1.6;
    overflow-x: hidden;
    opacity: 0;
}

/* 页面加载动画 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

/* Navbar Styles */
.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 5%;
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 20px rgba(0,0,0,0.1);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
    transition: all 0.3s ease;
}

.navbar.scrolled {
    padding: 0.5rem 5%;
    box-shadow: 0 2px 30px rgba(0,0,0,0.15);
}

.logo h1 {
    color: var(--primary-color);
    font-size: 1.5rem;
    font-weight: 700;
    animation: slideInLeft 0.8s ease;
}

.nav-links {
    display: flex;
    list-style: none;
    gap: 2rem;
    animation: fadeInUp 0.8s ease 0.2s both;
}

.nav-links a {
    text-decoration: none;
    color: var(--text-color);
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
}

.nav-links a::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -5px;
    left: 0;
    background: var(--gradient-primary);
    transition: width 0.3s ease;
}

.nav-links a:hover::after,
.nav-links a.active::after {
    width: 100%;
}

.nav-links a:hover,
.nav-links a.active {
    color: var(--primary-color);
}

.waitlist-btn {
    background: var(--gradient-primary);
    color: white;
    border: none;
    padding: 0.8rem 1.5rem;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
    animation: slideInRight 0.8s ease 0.4s both;
    position: relative;
    overflow: hidden;
}

.waitlist-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.waitlist-btn:hover::before {
    left: 100%;
}

.waitlist-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
}

/* Hero Section */
.hero {
    padding: 8rem 5% 4rem;
    text-align: center;
    background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(248,249,250,0.9)),
                url('assets/student-bg.svg') center/cover;
    min-height: 80vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 70%, rgba(76, 175, 80, 0.1), transparent),
                radial-gradient(circle at 70% 30%, rgba(33, 150, 243, 0.1), transparent);
    pointer-events: none;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-content h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: var(--text-color);
    animation: fadeInUp 1s ease 0.2s both;
}

.hero-content h2 {
    font-size: 1.5rem;
    color: #666;
    margin-bottom: 3rem;
    animation: fadeInUp 1s ease 0.4s both;
}

/* Golden Circle */
.golden-circle {
    position: relative;
    width: 300px;
    height: 300px;
    margin: 0 auto;
    animation: fadeInUp 1s ease 0.6s both;
}

.circle {
    position: absolute;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: white;
    transition: all 0.3s ease;
    cursor: pointer;
}

.circle:hover {
    transform: translate(-50%, -50%) scale(1.1);
}

/* 确保Why圆圈的hover效果也保持居中 */
.why.position-center:hover {
    transform: translate(-50%, -50%) scale(1.1);
}

.why {
    width: 100px;
    height: 100px;
    background: var(--gradient-primary);
    z-index: 3;
    animation: pulse 2s infinite;
    font-size: 0.9rem;
    /* 移除默认位置，让position-center类来控制位置 */
}

/* Why圆圈的多种位置选项 */
.why.position-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%); /* 确保完全居中 */
    z-index: 3; /* 确保Why圆圈在How圆圈之上 */
}

.why.position-top-left {
    position: absolute;
    top: 35%;
    left: 35%;
    transform: translate(-50%, -50%);
}

.why.position-top-right {
    position: absolute;
    top: 35%;
    left: 65%;
    transform: translate(-50%, -50%);
}

.why.position-bottom-left {
    position: absolute;
    top: 65%;
    left: 35%;
    transform: translate(-50%, -50%);
}

.why.position-bottom-right {
    position: absolute;
    /* 移动到How圆圈外部的右下方 */
    top: 75%;
    left: 80%;
    transform: translate(-50%, -50%);
}

.why.position-left {
    position: absolute;
    top: 50%;
    left: 30%;
    transform: translate(-50%, -50%);
}

.why.position-right {
    position: absolute;
    top: 50%;
    left: 70%;
    transform: translate(-50%, -50%);
}

.why.position-top {
    position: absolute;
    top: 30%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.why.position-bottom {
    position: absolute;
    top: 70%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.how {
    width: 200px;
    height: 200px;
    background: linear-gradient(135deg, #888, #666);
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 2;
    opacity: 0.8;
    font-size: 1rem;
    align-items: center; /* 改为center以便Why圆圈居中显示 */
    justify-content: center;
}

.what {
    width: 300px;
    height: 300px;
    background: linear-gradient(135deg, #666, #444);
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1;
    opacity: 0.6;
    font-size: 1.1rem;
    align-items: flex-start;
    padding-top: 15px;
}

/* Features Section */
.features {
    padding: 4rem 5%;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.feature-card {
    background-color: var(--card-background);
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    animation: fadeInUp 0.8s ease both;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.feature-card:hover::before {
    transform: scaleX(1);
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
}

.feature-card h3 {
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
}

/* Subject Icons */
.subject-icons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 1rem;
    text-align: center;
}

.subject-icon {
    transition: transform 0.3s ease;
}

.subject-icon:hover {
    transform: translateY(-5px);
}

.subject-icon i {
    font-size: 2rem;
    color: var(--secondary-color);
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
}

.subject-icon:hover i {
    color: var(--primary-color);
    animation: float 1s ease infinite;
}

/* Team Preview */
.team-preview {
    display: flex;
    gap: 1rem;
    overflow-x: auto;
    padding: 1rem 0;
}

/* Resource Preview */
.resource-preview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
}

.resource-item {
    text-align: center;
    padding: 1rem;
    background: linear-gradient(135deg, #f5f5f5, #e8e8e8);
    border-radius: 12px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.resource-item:hover {
    transform: translateY(-5px);
    background: var(--gradient-primary);
    color: white;
}

.resource-item i {
    font-size: 2rem;
    color: var(--secondary-color);
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
}

.resource-item:hover i {
    color: white;
    animation: pulse 0.5s ease;
}

/* 滚动动画触发 */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s ease;
}

.animate-on-scroll.animated {
    opacity: 1;
    transform: translateY(0);
}

/* 统计数据动画增强 */
.stats-section.animate-on-scroll {
    transform: translateY(50px);
}

.stats-section.animate-on-scroll.animated {
    transform: translateY(0);
}

.stat-item {
    padding: 1.5rem;
    transition: transform 0.3s ease;
}

.stat-item:hover {
    transform: scale(1.05);
}

/* 特色功能卡片动画 */
.highlight-card {
    background: white;
    padding: 2.5rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    animation: fadeInUp 0.8s ease both;
}

.highlight-card:nth-child(odd) {
    animation-delay: 0.1s;
}

.highlight-card:nth-child(even) {
    animation-delay: 0.2s;
}

/* 用户评价卡片动画 */
.testimonial-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    position: relative;
    transition: all 0.3s ease;
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
}

/* 新闻卡片动画 */
.news-card {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.news-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
}

/* 合作伙伴logo动画 */
.partner-logo {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
}

.partner-logo:hover {
    transform: scale(1.05) rotate(2deg);
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

/* 自定义工具提示样式 */
.custom-tooltip {
    position: absolute;
    background: rgba(0,0,0,0.9);
    color: white;
    padding: 10px 15px;
    border-radius: 8px;
    font-size: 14px;
    max-width: 200px;
    z-index: 1000;
    opacity: 0;
    transform: translateY(10px);
    transition: all 0.3s ease;
    pointer-events: none;
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
}

/* Footer */
footer {
    background: var(--gradient-primary);
    color: white;
    text-align: center;
    padding: 3rem 2rem;
    margin-top: 4rem;
    position: relative;
    overflow: hidden;
}

footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 20"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="20" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

footer p {
    position: relative;
    z-index: 2;
    font-size: 1.1rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .highlights-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }
    
    .testimonials-grid {
        grid-template-columns: 1fr;
    }
    
    .news-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }
}

@media (max-width: 768px) {
    .nav-links {
        display: none;
    }
    
    .hero-content h1 {
        font-size: 2rem;
    }
    
    .hero-content h2 {
        font-size: 1.2rem;
    }
    
    .golden-circle {
        width: 200px;
        height: 200px;
    }
    
    .why {
        width: 70px;
        height: 70px;
        font-size: 0.7rem;
    }
    
    .how {
        width: 140px;
        height: 140px;
        font-size: 0.8rem;
    }
    
    .what {
        width: 200px;
        height: 200px;
        font-size: 0.9rem;
        padding-top: 12px;
    }
    
    .features {
        grid-template-columns: 1fr;
    }
    
    .navbar {
        padding: 1rem 3%;
    }
    
    .hero {
        padding: 6rem 3% 3rem;
    }
    
    .section-header h2 {
        font-size: 2rem;
    }
    
    .stat-number {
        font-size: 2.5rem;
    }
    
    .cta-content h2 {
        font-size: 2rem;
    }
    
    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .highlights-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .stats-container {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }
    
    .partners-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 480px) {
    .hero {
        padding: 5rem 2% 2rem;
    }
    
    .section-header h2 {
        font-size: 1.8rem;
    }
    
    .highlight-card,
    .testimonial-card,
    .news-card {
        padding: 1.5rem;
    }
    
    .stats-container {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .stat-number {
        font-size: 2rem;
    }
    
    .partners-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .cta-content {
        padding: 0 1rem;
    }
} 